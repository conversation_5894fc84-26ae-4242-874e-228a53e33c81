# 📝 CloudStudio 监控管理系统更新日志

所有重要的项目变更都会记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
项目遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范。

## [未发布]

### 计划中的功能
- 邮件/短信告警通知
- 监控数据导出功能
- 多用户权限管理
- 监控模板功能
- API 密钥认证
- Webhook 集成

---

## [1.0.1] - 2025-01-31

### 🚀 新增功能
- **系统日志查看器**: 前端日志查看系统，支持多级别过滤
- **监控状态图表**: 基于 Chart.js 的可视化监控状态图表
- **HTTP 方法选择**: 支持 GET、POST、HEAD 方法选择
- **完整技术文档**: 新增架构文档、API 文档、部署指南

### 🔧 改进优化
- **文档重构**: 修正所有文档中的不一致信息
- **部署流程**: 简化部署流程，移除对不存在文件的引用
- **错误处理**: 改进 HTTP 405 错误的处理和提示
- **配置管理**: 优化 deno.json 配置文件

### 🐛 问题修复
- 修复 bilibili.com 等网站的 HTTP 405 错误
- 修正 README 中的文件结构描述
- 修复部署脚本中的文件引用错误
- 统一版本号信息

### 📚 文档更新
- 新增 `ARCHITECTURE.md` - 系统架构文档
- 新增 `API.md` - 完整 API 文档
- 新增 `DEPLOYMENT.md` - 详细部署指南
- 新增 `CHANGELOG.md` - 版本更新日志
- 更新 `README.md` 和 `README_CN.md`
- 更新 `deploy.sh` 部署脚本

### 🔄 技术变更
- 移除对 `cloudStudioRefresh.prod.ts` 的引用
- 优化 Deno KV 数据存储结构
- 改进监控调度器性能
- 增强错误日志记录

---

## [1.0.0] - 2025-01-30

### 🎉 首次发布

#### 核心功能
- **Web 管理界面**: 现代化响应式设计的管理界面
- **多站点监控**: 支持同时监控多个网站和服务
- **身份验证系统**: 基于会话的安全认证机制
- **数据持久化**: 基于 Deno KV 的数据存储系统
- **实时监控**: 自动定时监控任务执行
- **历史记录**: 完整的监控历史数据追踪
- **单文件部署**: 零依赖的单文件部署方案

#### 技术特性
- **Deno 运行时**: 基于 Deno 2.3.5+ 构建
- **TypeScript**: 使用 TypeScript 严格模式开发
- **Deno KV**: 内置键值数据库存储
- **HTTP 服务器**: 使用 Deno 内置 HTTP 服务器
- **响应式设计**: 支持桌面和移动端访问

#### 安全特性
- **密码认证**: 硬编码密码保护
- **会话管理**: 安全的 Cookie 会话系统
- **登录限制**: 防暴力破解的频率限制
- **CSRF 防护**: 跨站请求伪造防护
- **安全头**: 完整的 HTTP 安全响应头

#### 监控功能
- **HTTP 监控**: 支持 GET、POST、HEAD 方法
- **自定义间隔**: 1-60 分钟可配置监控间隔
- **Cookie 支持**: 支持携带 Cookie 的监控请求
- **自定义头**: 支持自定义 HTTP 请求头
- **超时控制**: 可配置的请求超时时间
- **并发监控**: 支持多个监控任务并发执行

#### 数据管理
- **监控配置**: 完整的监控配置 CRUD 操作
- **历史记录**: 详细的监控执行历史
- **系统日志**: 结构化的系统运行日志
- **会话存储**: 安全的用户会话管理
- **数据清理**: 自动清理过期数据

#### API 接口
- **RESTful API**: 完整的 REST API 接口
- **JSON 格式**: 统一的 JSON 数据交换格式
- **错误处理**: 标准化的错误响应格式
- **状态码**: 标准 HTTP 状态码使用

#### 部署支持
- **Deno Deploy**: 原生支持 Deno Deploy 平台
- **本地部署**: 支持本地开发和部署
- **Docker 支持**: 提供 Docker 部署方案
- **环境变量**: 灵活的环境变量配置

#### 开发工具
- **代码检查**: 内置代码质量检查
- **格式化**: 自动代码格式化
- **Lint 检查**: 代码规范检查
- **集成测试**: 完整的集成测试套件
- **部署脚本**: 自动化部署脚本

---

## 版本说明

### 版本号规则
项目遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范：

- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 发布周期
- **主版本**: 根据重大功能更新发布
- **次版本**: 每月发布一次功能更新
- **修订版本**: 根据需要发布问题修复

### 支持政策
- **当前版本**: 提供完整支持和更新
- **前一版本**: 提供安全更新和重要问题修复
- **更早版本**: 仅提供安全更新

---

## 贡献指南

### 如何贡献
1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 提交规范
使用 [约定式提交](https://www.conventionalcommits.org/zh-hans/v1.0.0/) 规范：

- `feat`: 新功能
- `fix`: 问题修复
- `docs`: 文档更新
- `style`: 代码格式修改
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 问题报告
请使用 GitHub Issues 报告问题，包含：
- 问题描述
- 复现步骤
- 期望行为
- 实际行为
- 环境信息

---

## 致谢

感谢所有为 CloudStudio 监控管理系统做出贡献的开发者和用户。

特别感谢：
- Deno 团队提供优秀的运行时环境
- Chart.js 团队提供强大的图表库
- 所有提供反馈和建议的用户
